import Foundation
import CloudKit

/// Model voor een tafeltennis speler
struct Player: Identifiable, Codable, Hashable {
    let id: UUID
    var name: String
    var eloRating: Double
    var matchesPlayed: Int
    var matchesWon: Int
    var gamesPlayed: Int
    var gamesWon: Int
    var createdAt: Date
    var competitionId: UUID // Referentie naar de competitie
    var profileImageData: Data? // Optionele profielfoto als Data

    init(name: String, competitionId: UUID, eloRating: Double = 1200.0, profileImageData: Data? = nil) {
        self.id = UUID()
        self.name = name
        self.competitionId = competitionId
        self.eloRating = eloRating
        self.matchesPlayed = 0
        self.matchesWon = 0
        self.gamesPlayed = 0
        self.gamesWon = 0
        self.createdAt = Date()
        self.profileImageData = profileImageData
    }
    
    /// Berekent het win percentage voor wedstrijden
    var matchWinPercentage: Double {
        guard matchesPlayed > 0 else { return 0.0 }
        return Double(matchesWon) / Double(matchesPlayed) * 100.0
    }
    
    /// Berekent het win percentage voor games
    var gameWinPercentage: Double {
        guard gamesPlayed > 0 else { return 0.0 }
        return Double(gamesWon) / Double(gamesPlayed) * 100.0
    }
    
    /// Berekent het aantal verloren wedstrijden
    var matchesLost: Int {
        return matchesPlayed - matchesWon
    }
    
    /// Berekent het aantal verloren games
    var gamesLost: Int {
        return gamesPlayed - gamesWon
    }
}

/// Extensie voor ELO berekeningen
extension Player {
    /// Berekent de verwachte score tegen een andere speler
    func expectedScore(against opponent: Player) -> Double {
        let ratingDifference = opponent.eloRating - self.eloRating
        return 1.0 / (1.0 + pow(10.0, ratingDifference / 400.0))
    }

    /// Berekent de nieuwe ELO rating na een wedstrijd
    func newEloRating(against opponent: Player, actualScore: Double, kFactor: Double = 32.0) -> Double {
        let expectedScore = self.expectedScore(against: opponent)
        return self.eloRating + kFactor * (actualScore - expectedScore)
    }
}

// MARK: - CloudKit Support
extension Player {
    static let recordType = "Player"

    /// Initializer vanuit CloudKit CKRecord
    init?(from record: CKRecord) {
        guard let name = record["name"] as? String,
              let competitionIdString = record["competitionId"] as? String,
              let competitionId = UUID(uuidString: competitionIdString),
              let eloRating = record["eloRating"] as? Double,
              let matchesPlayed = record["matchesPlayed"] as? Int,
              let matchesWon = record["matchesWon"] as? Int,
              let gamesPlayed = record["gamesPlayed"] as? Int,
              let gamesWon = record["gamesWon"] as? Int,
              let createdAt = record["createdAt"] as? Date,
              let idString = record.recordID.recordName.components(separatedBy: "_").last,
              let id = UUID(uuidString: idString) else {
            return nil
        }

        self.id = id
        self.name = name
        self.competitionId = competitionId
        self.eloRating = eloRating
        self.matchesPlayed = matchesPlayed
        self.matchesWon = matchesWon
        self.gamesPlayed = gamesPlayed
        self.gamesWon = gamesWon
        self.createdAt = createdAt

        // Optionele profielfoto uit CloudKit
        if let asset = record["profileImage"] as? CKAsset,
           let data = try? Data(contentsOf: asset.fileURL!) {
            self.profileImageData = data
        } else {
            self.profileImageData = nil
        }
    }

    /// Converteert naar CloudKit CKRecord
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: "Player_\(id.uuidString)")
        let record = CKRecord(recordType: Player.recordType, recordID: recordID)

        record["name"] = name
        record["competitionId"] = competitionId.uuidString
        record["eloRating"] = eloRating
        record["matchesPlayed"] = matchesPlayed
        record["matchesWon"] = matchesWon
        record["gamesPlayed"] = gamesPlayed
        record["gamesWon"] = gamesWon
        record["createdAt"] = createdAt

        // Voeg profielfoto toe als CKAsset
        if let imageData = profileImageData {
            let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("\(id.uuidString)_profile.jpg")
            do {
                try imageData.write(to: tempURL)
                let asset = CKAsset(fileURL: tempURL)
                record["profileImage"] = asset
            } catch {
                print("Error writing profile image to temp file: \(error)")
            }
        }

        return record
    }

    /// Converteert naar CloudKit CKRecord met bestaand record (voor updates)
    func toCKRecord(existingRecord: CKRecord) -> CKRecord {
        existingRecord["name"] = name
        existingRecord["competitionId"] = competitionId.uuidString
        existingRecord["eloRating"] = eloRating
        existingRecord["matchesPlayed"] = matchesPlayed
        existingRecord["matchesWon"] = matchesWon
        existingRecord["gamesPlayed"] = gamesPlayed
        existingRecord["gamesWon"] = gamesWon
        existingRecord["createdAt"] = createdAt

        // Update profielfoto als CKAsset
        if let imageData = profileImageData {
            let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("\(id.uuidString)_profile.jpg")
            do {
                try imageData.write(to: tempURL)
                let asset = CKAsset(fileURL: tempURL)
                existingRecord["profileImage"] = asset
            } catch {
                print("Error writing profile image to temp file: \(error)")
            }
        } else {
            // Verwijder profielfoto als deze nil is
            existingRecord["profileImage"] = nil
        }

        return existingRecord
    }
}

// MARK: - Custom Codable Implementation for Match Storage
extension Player {
    /// Custom coding keys die profileImageData uitsluit voor Match records
    private enum MatchCodingKeys: String, CodingKey {
        case id, name, eloRating, matchesPlayed, matchesWon
        case gamesPlayed, gamesWon, createdAt, competitionId
        // profileImageData wordt bewust weggelaten om CloudKit record size te beperken
    }

    /// Encodeert Player zonder profileImageData voor gebruik in Match records
    func encodeForMatch() throws -> Data {
        let encoder = JSONEncoder()
        var copy = self
        copy.profileImageData = nil // Verwijder profile image data om record size te beperken
        return try encoder.encode(copy)
    }
}
